import { createSupabaseServerClient } from "@/lib/supabase/client"
import { NextRequest, NextResponse } from "next/server"
import { stripe, calculateApplicationFee } from "@/lib/stripe"

export async function POST(request: NextRequest) {
  try {
    if (!stripe) {
      return NextResponse.json(
        { error: "Stripe is not configured" },
        { status: 503 }
      )
    }

    const { writerId, entryId } = await request.json()

    if (!writerId) {
      return NextResponse.json(
        { error: "Writer ID is required" },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get writer information (unified system - all users can receive subscriptions)
    const { data: writer, error: writerError } = await supabase
      .from("users")
      .select("id, name, price_monthly, stripe_account_id, stripe_onboarding_complete")
      .eq("id", writerId)
      .single()

    if (writerError || !writer) {
      return NextResponse.json(
        { error: "Writer not found" },
        { status: 404 }
      )
    }

    // Check if writer has premium content but no pricing set
    const subscriptionPrice = writer.price_monthly
    const hasNoPricing = subscriptionPrice === null || subscriptionPrice === 0

    console.log('Subscribe API - Writer info:', {
      writerId,
      writerName: writer.name,
      priceMonthly: writer.price_monthly,
      hasNoPricing,
      stripeAccountId: writer.stripe_account_id,
      stripeOnboardingComplete: writer.stripe_onboarding_complete
    })

    // Check if this writer has any paid posts
    const { data: paidPosts } = await supabase
      .from("diary_entries")
      .select("id")
      .eq("user_id", writerId)
      .eq("is_free", false)
      .eq("is_hidden", false)
      .limit(1)

    const hasPaidContent = paidPosts && paidPosts.length > 0

    // If writer has paid content but no pricing set, they need to set up pricing
    if (hasPaidContent && hasNoPricing) {
      return NextResponse.json(
        { error: "This writer has premium content but hasn't set up subscription pricing yet. Please ask them to set their monthly subscription price in their profile settings." },
        { status: 400 }
      )
    }

    // If writer has no paid content, treat as free follow
    if (!hasPaidContent) {
      const { data: existingFollow } = await supabase
        .from("follows")
        .select("id")
        .eq("follower_id", user.id)
        .eq("writer_id", writerId)
        .single()

      if (existingFollow) {
        return NextResponse.json(
          { error: "You are already following this user" },
          { status: 400 }
        )
      }

      // Create follow relationship for free content
      const { error: followError } = await supabase
        .from("follows")
        .insert({
          follower_id: user.id,
          writer_id: writerId
        })

      if (followError) {
        console.error('Follow error:', followError)
        return NextResponse.json(
          { error: `Failed to follow user: ${followError.message}` },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: "Successfully followed user!",
        redirect: `/u/${writerId}?followed=true`
      })
    }

    // Writer has paid content and pricing set - create paid subscription
    if (!writer.stripe_account_id || !writer.stripe_onboarding_complete) {
      return NextResponse.json(
        { error: "Payment setup incomplete. Please complete Stripe Connect onboarding first." },
        { status: 400 }
      )
    }

    // Check if user already has an active subscription
    const { data: existingSubscription } = await supabase
      .from("subscriptions")
      .select("id, active_until")
      .eq("subscriber_id", user.id)
      .eq("writer_id", writerId)
      .gte("active_until", new Date().toISOString())
      .single()

    if (existingSubscription) {
      return NextResponse.json(
        { error: "You already have an active subscription to this writer" },
        { status: 400 }
      )
    }

    console.log('Creating paid subscription for writer with price:', subscriptionPrice)

    // Subscription check already done above for paid writers

    // Calculate application fee using new "Percentage + Fixed Fee" model
    const { applicationFeeAmount, writerAmount } = calculateApplicationFee(writer.price_monthly, 'subscription')

    // Create Stripe Checkout Session with Connect
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      mode: "subscription",
      customer_email: user.email,
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `Subscription to ${writer.name || "Writer"}`,
              description: `Monthly subscription to access all diary entries`,
            },
            unit_amount: writer.price_monthly,
            recurring: {
              interval: "month",
            },
          },
          quantity: 1,
        },
      ],
      subscription_data: {
        application_fee_amount: applicationFeeAmount, // New fixed + percentage fee model
        transfer_data: {
          destination: writer.stripe_account_id,
        },
        metadata: {
          subscriber_id: user.id,
          writer_id: writerId,
          platform_fee: applicationFeeAmount.toString(),
          writer_amount: writerAmount.toString(),
        },
      },
      metadata: {
        subscriber_id: user.id,
        writer_id: writerId,
        type: "subscription",
        platform_fee: applicationFeeAmount.toString(),
        writer_amount: writerAmount.toString(),
      },
      success_url: `${request.nextUrl.origin}/u/${writerId}?subscribed=true`,
      cancel_url: `${request.nextUrl.origin}/u/${writerId}?cancelled=true`,
    })

    return NextResponse.json({ url: session.url })

  } catch (error) {
    console.error("Error creating subscription:", error)
    return NextResponse.json(
      { error: "Failed to create subscription" },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OnlyDiary Subscription API',
    version: '1.0.0',
    endpoints: {
      POST: 'Create new subscription with Stripe',
    }
  })
}