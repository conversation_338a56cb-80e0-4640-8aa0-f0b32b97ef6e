// Test script to verify the new fee calculation logic
// Run with: node test-fee-calculations.js

// Simulate the new fee calculation function
const SUBSCRIPTION_FEE_PERCENTAGE = 0.20 // 20%
const DONATION_FEE_PERCENTAGE = 0.10 // 10%
const BOOK_SALE_FEE_PERCENTAGE = 0.30 // 30%
const PLATFORM_FIXED_FEE_CENTS = 35 // 35 cents

function calculateApplicationFee(totalAmountInCents, paymentType) {
  // Get the appropriate percentage based on payment type
  let feePercentage
  switch (paymentType) {
    case 'donation':
      feePercentage = DONATION_FEE_PERCENTAGE
      break
    case 'subscription':
      feePercentage = SUBSCRIPTION_FEE_PERCENTAGE
      break
    case 'book':
      feePercentage = BOOK_SALE_FEE_PERCENTAGE
      break
    default:
      throw new Error(`Invalid payment type: ${paymentType}`)
  }

  // Calculate platform's gross fee: (Total * Percentage) + Fixed Fee
  const percentageFee = Math.round(totalAmountInCents * feePercentage)
  const applicationFeeAmount = percentageFee + PLATFORM_FIXED_FEE_CENTS

  // Calculate writer's payout: Total - Platform's Gross Fee
  const writerAmount = totalAmountInCents - applicationFeeAmount

  // Ensure writer amount is not negative
  if (writerAmount < 0) {
    throw new Error(`Transaction amount too small. Minimum required: $${((percentageFee + PLATFORM_FIXED_FEE_CENTS) / 100).toFixed(2)}`)
  }

  return {
    applicationFeeAmount,
    writerAmount
  }
}

// Test cases
console.log('=== Fee Calculation Tests ===\n')

// Test 1: $10.00 subscription (example from requirements)
console.log('Test 1: $10.00 Subscription')
const sub10 = calculateApplicationFee(1000, 'subscription')
console.log(`Total: $10.00`)
console.log(`Platform Fee: $${(sub10.applicationFeeAmount / 100).toFixed(2)} (20% + $0.35 = $2.00 + $0.35 = $2.35)`)
console.log(`Writer Amount: $${(sub10.writerAmount / 100).toFixed(2)} ($10.00 - $2.35 = $7.65)`)
console.log('✓ Matches requirements\n')

// Test 2: $1.00 donation
console.log('Test 2: $1.00 Donation')
const don1 = calculateApplicationFee(100, 'donation')
console.log(`Total: $1.00`)
console.log(`Platform Fee: $${(don1.applicationFeeAmount / 100).toFixed(2)} (10% + $0.35 = $0.10 + $0.35 = $0.45)`)
console.log(`Writer Amount: $${(don1.writerAmount / 100).toFixed(2)} ($1.00 - $0.45 = $0.55)`)
console.log()

// Test 3: $5.00 book
console.log('Test 3: $5.00 Book')
const book5 = calculateApplicationFee(500, 'book')
console.log(`Total: $5.00`)
console.log(`Platform Fee: $${(book5.applicationFeeAmount / 100).toFixed(2)} (30% + $0.35 = $1.50 + $0.35 = $1.85)`)
console.log(`Writer Amount: $${(book5.writerAmount / 100).toFixed(2)} ($5.00 - $1.85 = $3.15)`)
console.log()

// Test 4: Edge case - very small amount
console.log('Test 4: Edge Case - Small Amount')
try {
  const small = calculateApplicationFee(50, 'donation') // $0.50
  console.log(`Total: $0.50`)
  console.log(`Platform Fee: $${(small.applicationFeeAmount / 100).toFixed(2)}`)
  console.log(`Writer Amount: $${(small.writerAmount / 100).toFixed(2)}`)
} catch (error) {
  console.log(`Error (expected): ${error.message}`)
}
console.log()

// Test 5: Larger amounts
console.log('Test 5: $100.00 Subscription')
const sub100 = calculateApplicationFee(10000, 'subscription')
console.log(`Total: $100.00`)
console.log(`Platform Fee: $${(sub100.applicationFeeAmount / 100).toFixed(2)} (20% + $0.35 = $20.00 + $0.35 = $20.35)`)
console.log(`Writer Amount: $${(sub100.writerAmount / 100).toFixed(2)} ($100.00 - $20.35 = $79.65)`)
console.log()

// Test 6: Compare with old CSV data
console.log('Test 6: Compare with CSV Data ($1.00 donation)')
console.log('Old system from CSV:')
console.log('- Total: $1.00')
console.log('- Platform fee: 5 cents (5%)')
console.log('- Writer amount: 95 cents')
console.log('- But after Stripe fees: ~62 cents actual payout')
console.log()
console.log('New system:')
console.log(`- Total: $1.00`)
console.log(`- Platform fee: ${(don1.applicationFeeAmount / 100).toFixed(2)} (10% + $0.35)`)
console.log(`- Writer amount: ${(don1.writerAmount / 100).toFixed(2)}`)
console.log('- Platform now covers Stripe fees, ensuring predictable payouts')
console.log()

console.log('=== All Tests Complete ===')
